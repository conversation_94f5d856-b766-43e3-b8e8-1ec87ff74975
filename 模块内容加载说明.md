# 模块内容加载功能说明

## 问题描述

章节的模块内容没有显示出来，需要通过 `/tp/content/{id}` 接口获取模块的详细内容。

## 解决方案

### 1. 添加获取模块内容的API

在 `src/api/lessonPlan.js` 中添加了新的API方法：

```javascript
/**
 * 获取模块内容详细信息
 * @param {string|number} contentId 内容ID
 * @returns {Promise<Object>} 模块内容详情数据
 */
export const getModuleContentById = (contentId) => {
  console.log(`正在获取模块内容详情: ${contentId}`);
  return request({
    url: `/tp/content/${contentId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取模块内容${contentId}详情成功:`, response);
    return response;
  }).catch(error => {
    console.error(`获取模块内容 ${contentId} 详情失败:`, error);
    return Promise.reject(error);
  });
};
```

### 2. 修改章节数据转换逻辑

将 `convertLessonPlanToChapters` 函数改为异步函数，在转换过程中加载每个模块的详细内容：

```javascript
const convertLessonPlanToChapters = async (lessonPlan) => {
  if (!lessonPlan || !lessonPlan.tpModuleList) {
    return [];
  }
  
  // 并行加载所有模块的详细内容
  const chaptersPromises = lessonPlan.tpModuleList.map(async (module, index) => {
    let detailedContent = module.content?.content || '暂无描述';
    
    // 如果模块有内容ID，获取详细内容
    if (module.content?.id) {
      try {
        const contentResponse = await getModuleContentById(module.content.id);
        if (contentResponse && contentResponse.code === 200 && contentResponse.data) {
          detailedContent = contentResponse.data.content || detailedContent;
        }
      } catch (error) {
        console.error(`获取模块${module.id}内容失败:`, error);
        // 如果获取失败，使用原有内容
      }
    }
    
    return {
      id: module.id,
      title: module.title || `第${index + 1}章`,
      description: detailedContent, // 使用详细内容
      // ... 其他字段
    };
  });
  
  // 等待所有内容加载完成
  const chapters = await Promise.all(chaptersPromises);
  return chapters;
};
```

### 3. 更新调用方式

由于转换函数现在是异步的，需要在调用时使用 `await`：

```javascript
// 将教案模块转换为章节格式（异步加载详细内容）
const convertedChapters = await convertLessonPlanToChapters(currentLessonPlan.value);
chapters.value = convertedChapters;
```

## 工作流程

1. **加载教案数据**：通过 `getLessonPlanById` 获取教案基本信息
2. **获取模块列表**：从教案数据中提取 `tpModuleList`
3. **并行加载内容**：对每个模块，如果有 `content.id`，则调用 `/tp/content/{id}` 获取详细内容
4. **转换为章节格式**：将加载到的详细内容设置为章节的 `description`
5. **显示章节**：在界面上显示包含详细内容的章节

## 错误处理

- 如果某个模块的内容加载失败，会使用原有的简短内容
- 不会因为单个模块内容加载失败而影响整个章节列表的显示
- 所有错误都会在控制台输出详细日志

## 性能优化

- 使用 `Promise.all` 并行加载所有模块内容，而不是串行加载
- 减少了总的加载时间

## 测试验证

1. **查看控制台日志**：
   ```
   正在获取模块内容详情: 123
   获取模块内容123详情成功: {...}
   获取模块1的详细内容: {...}
   ```

2. **检查章节显示**：
   - 展开章节后应该能看到详细的内容描述
   - 内容应该是从 `/tp/content/{id}` 接口获取的完整内容

3. **验证数据结构**：
   - 确保模块数据中包含 `content.id` 字段
   - 确保 `/tp/content/{id}` 接口返回正确的数据格式

## 注意事项

1. **数据依赖**：模块必须有 `content.id` 才能加载详细内容
2. **API格式**：确保 `/tp/content/{id}` 接口返回标准的 AjaxResult 格式
3. **加载时间**：如果模块很多，可能会增加页面加载时间
4. **网络错误**：需要处理网络异常情况

## 后续优化建议

1. **缓存机制**：可以添加内容缓存，避免重复加载
2. **懒加载**：只在展开章节时才加载详细内容
3. **分页加载**：如果章节很多，可以考虑分页加载
4. **加载状态**：添加内容加载的loading状态提示
