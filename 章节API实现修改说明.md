# 章节API实现修改说明

## 修改概述

根据需求，已将教师端课程详细页面的章节模块从模拟数据改为使用API调用的方式实现。章节功能现在与教案逻辑关联，通过教案API来管理章节数据。

## 主要修改内容

### 1. 删除的旧代码
- 删除了 `CourseChapters.vue` 中的模拟数据代码（原第141-202行）
- 删除了所有TODO注释的占位符方法

### 2. 新增的API导入
```javascript
import { getCourseById, updateCourse } from '@/api/courses';
import { createLessonPlan, getLessonPlanById, updateLessonPlan } from '@/api/lessonPlan';
import { getCurrentUser } from '@/api/auth';
```

### 3. 核心实现逻辑

#### 3.1 课程与教案关联逻辑
- `ensureCourseHasLessonPlan()`: 确保课程有关联的教案
  - 获取课程详情，检查 `tpId` 字段
  - 如果 `tpId` 为空，创建默认教案
  - 更新课程的 `tpId` 字段，建立关联关系

#### 3.2 数据转换逻辑
- `convertLessonPlanToChapters()`: 将教案数据转换为章节显示格式
- `convertChapterToModule()`: 将章节数据转换为教案模块格式

#### 3.3 章节操作方法
- `addNewChapter()`: 新增章节（创建新的教案模块）
- `editChapter()`: 编辑章节（更新教案模块）
- `deleteChapter()`: 删除章节（删除教案模块）
- `updateLessonPlanData()`: 统一的教案数据更新方法

## 数据结构映射

### 教案 -> 章节的映射关系
```
TpPlan (教案) -> 课程的章节容器
├── tpModuleList (教案模块列表) -> 章节列表
    ├── TpModule (教案模块) -> 具体章节
        ├── title -> 章节标题
        ├── sort -> 章节排序
        └── content (TpContent) -> 章节内容
            ├── content -> 章节描述
            └── fileUrl -> 附件链接
```

### 章节显示格式
```javascript
{
  id: module.id,
  title: module.title,
  description: module.content?.content,
  status: 'published',
  hours: 2,
  difficulty: 'medium',
  progress: 0,
  expanded: false,
  sections: [],
  _moduleData: module // 保存原始模块数据
}
```

## API调用流程

### 初始化流程
1. `getCourseById(courseId)` - 获取课程信息
2. 检查课程的 `tpId` 字段
3. 如果 `tpId` 为空：
   - `createLessonPlan(defaultData)` - 创建默认教案
   - `updateCourse(courseData)` - 更新课程 `tpId`
4. `getLessonPlanById(tpId)` - 获取教案详情
5. 转换教案数据为章节格式并显示

### 章节操作流程
1. 用户执行章节操作（增删改）
2. 更新本地章节数据
3. `updateLessonPlanData()` - 将章节数据转换为教案格式并更新
4. 重新加载教案数据确保同步

## 错误处理

- 所有API调用都包含错误处理
- 操作失败时会回滚本地数据
- 提供用户友好的错误提示
- 确保数据一致性

## 注意事项

1. **小节功能**: 当前版本暂未实现小节功能，用户可在章节描述中添加详细内容
2. **排序功能**: 章节排序功能待后续实现
3. **权限控制**: 需要确保当前用户有操作权限
4. **数据同步**: 所有操作都会同步到后端教案数据

## 使用的接口

- `POST /tp/plan` - 创建教案
- `GET /tp/plan/{id}` - 获取教案详情  
- `PUT /tp/plan` - 更新教案
- `GET /core/courses/{id}` - 获取课程详情
- `PUT /core/courses` - 更新课程信息

## 测试建议

1. 测试新课程（无tpId）的章节创建
2. 测试已有教案的课程章节加载
3. 测试章节的增删改操作
4. 测试错误情况的处理
5. 验证数据在前后端的一致性
